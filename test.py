import requests
import json

headers = {
    "accept": "application/json",
    "accept-language": "zh-CN,zh;q=0.9",
    "app-type": "web",
    "referer": "https://web3.okx.com/zh-hans/token/solana/2hKSxkRZHkWJ3Mr5Fp9gRgLr4pbgP5UcYciCjvdppump",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "sec-ch-ua-platform": "\"Windows\"",
    "sec-fetch-dest": "empty",
    "sec-fetch-mode": "cors",
    "sec-fetch-site": "same-origin",
    "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36",
    "x-cdn": "https://web3.okx.com",
    "x-utc": "8",
    "x-zkdex-env": "0"
}
cookies = {
    "_gcl_gs": "2.1.k1$i1747121212$u3801134",
    "ok_global": "{%22g_t%22:2}",
    "fp_s": "0",
    "_ga_G0EKWWQGTZ": "GS2.1.s1752903913$o153$g0$t1752903913$j60$l0$h0",
    "__cf_bm": "DITCT32q3aoYh2j8leor_UW4Te7Hc_Nux831MD_WBEU-1752905362-*******-Y0yOPbwj08tGv4Kc.dIrriG5Te28bj.oR8K00hJKq3ruN4QDhQY.qYnfuaTy1.BvhMP_UG1NgdEIAaci2wc17s5QucvWIyRwoEtdAuH_EA8"
}
url = "https://web3.okx.com/priapi/v5/dex/token/market/history-dex-token-hlc-candles"
params = {
    "chainId": "501",
    "address": "2hKSxkRZHkWJ3Mr5Fp9gRgLr4pbgP5UcYciCjvdppump",
    "after": "1747584000000",
    "bar": "1m",
    "limit": "800",
    # "t": "1752905610738"
}
response = requests.get(url, headers=headers, cookies=cookies, params=params)

print(response.text)
data_num = json.loads(response.text).get("data")
print(len(data_num))
print(response)