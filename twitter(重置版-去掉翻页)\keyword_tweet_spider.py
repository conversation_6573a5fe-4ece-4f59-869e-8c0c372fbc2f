# !/usr/bin/python
# -*- coding: utf-8 -*-
"""
@Time    :  2025/2/11 13:45
@Python  :  Python3.7
@Desc    :  根据关键词搜索推文
"""

import requests
import json
import time
import random
import urllib.parse
import threading
import atexit
import signal
import sys
from loguru import logger
from datetime import datetime
from db import RedisClient
from Spiders.utils import DB_BASE


class XSpider(DB_BASE):

    def __init__(self, max_pages=None, start_time=None):
        super().__init__()
        self.max_pages = max_pages
        self.start_time = start_time
        self.redis_client = RedisClient(redis_name='cookies:twitter')
        self.redis_client_keywords = RedisClient(redis_name='keywords:coin')
        self.redis_processed_keywords = RedisClient(redis_name='keywords:processed_keywords')
        self.redis_comment_wait_spider = RedisClient(redis_name='twitter:comment_wait_spider')
        self.redis_is_using = RedisClient(redis_name='cookies:is_using')  # 添加cookie管理
        self.current_cookie_account = None  # 当前使用的账号名
        
        # 移除初始化时获取全局cookie的代码，避免多线程共用
        self.cookie_data = None
        self.headers = None
        self.cookies = None
        self.proxy = None
        
        self.base_params = {
            "variables": "{\"rawQuery\":\"\",\"count\":20,\"querySource\":\"typed_query\",\"product\":\"Top\"}",
            "features": "{\"rweb_video_screen_enabled\":false,\"payments_enabled\":false,\"profile_label_improvements_pcf_label_in_post_enabled\":true,\"rweb_tipjar_consumption_enabled\":true,\"verified_phone_label_enabled\":false,\"creator_subscriptions_tweet_preview_api_enabled\":true,\"responsive_web_graphql_timeline_navigation_enabled\":true,\"responsive_web_graphql_skip_user_profile_image_extensions_enabled\":false,\"premium_content_api_read_enabled\":false,\"communities_web_enable_tweet_community_results_fetch\":true,\"c9s_tweet_anatomy_moderator_badge_enabled\":true,\"responsive_web_grok_analyze_button_fetch_trends_enabled\":false,\"responsive_web_grok_analyze_post_followups_enabled\":true,\"responsive_web_jetfuel_frame\":false,\"responsive_web_grok_share_attachment_enabled\":true,\"articles_preview_enabled\":true,\"responsive_web_edit_tweet_api_enabled\":true,\"graphql_is_translatable_rweb_tweet_is_translatable_enabled\":true,\"view_counts_everywhere_api_enabled\":true,\"longform_notetweets_consumption_enabled\":true,\"responsive_web_twitter_article_tweet_consumption_enabled\":true,\"tweet_awards_web_tipping_enabled\":false,\"responsive_web_grok_show_grok_translated_post\":false,\"responsive_web_grok_analysis_button_from_backend\":true,\"creator_subscriptions_quote_tweet_preview_enabled\":false,\"freedom_of_speech_not_reach_fetch_enabled\":true,\"standardized_nudges_misinfo\":true,\"tweet_with_visibility_results_prefer_gql_limited_actions_policy_enabled\":true,\"longform_notetweets_rich_text_read_enabled\":true,\"longform_notetweets_inline_media_enabled\":true,\"responsive_web_grok_image_annotation_enabled\":true,\"responsive_web_enhance_cards_enabled\":false}"
        }
        
        # 注册程序退出时的清理函数
        atexit.register(self.cleanup_on_exit)
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

    def signal_handler(self, signum, frame):
        """处理程序中断信号"""
        logger.info("接收到退出信号，正在清理...")
        self.cleanup_on_exit()
        sys.exit(0)

    def cleanup_on_exit(self):
        """程序退出时清理cookies:is_using中的记录"""
        try:
            # 清理单线程的cookie记录
            if self.current_cookie_account:
                self.redis_is_using.delete("Krickliu_single")
                logger.success(f"已从cookies:is_using中移除单线程记录: Krickliu_single -> {self.current_cookie_account}")
                self.current_cookie_account = None
            
            # 清理所有多线程的cookie记录
            self.cleanup_all_thread_cookies()
            
        except Exception as e:
            logger.error(f"清理cookies:is_using记录失败: {e}")

    def cleanup_all_thread_cookies(self):
        """清理所有线程的cookie使用记录"""
        try:
            using_cookies = self.redis_is_using.all()
            cleaned_count = 0
            if using_cookies:
                for field_name in using_cookies.keys():
                    if field_name.startswith("Krickliu_thread_"):
                        self.redis_is_using.delete(field_name)
                        logger.info(f"已清理线程cookie记录: {field_name}")
                        cleaned_count += 1
            
            if cleaned_count > 0:
                logger.success(f"总共清理了 {cleaned_count} 个线程cookie记录")
            else:
                logger.info("没有找到需要清理的线程cookie记录")
                
        except Exception as e:
            logger.error(f"清理线程cookie记录失败: {e}")

    def get_cookie_from_redis(self, exclude_account=None):
        """从Redis中获取cookie信息
        Args:
            exclude_account: 要排除的账号名，用于切换到不同的cookie
        """
        try:
            # 获取正在使用的账号名列表
            using_cookies = self.redis_is_using.all()
            using_account_names = set(using_cookies.values()) if using_cookies else set()
            logger.info(f"当前正在使用的账号: {using_account_names}")
            
            # 获取所有cookie
            all_cookies = self.redis_client.all()
            if not all_cookies:
                logger.error("Redis中没有可用的cookie")
                return {}
            
            # 如果需要排除特定账号
            available_accounts = list(all_cookies.keys())
            if exclude_account and exclude_account in available_accounts:
                available_accounts.remove(exclude_account)
                logger.info(f"排除账号 {exclude_account}，剩余可用账号数: {len(available_accounts)}")
            
            # 排除正在使用的账号
            available_accounts = [account for account in available_accounts if account not in using_account_names]
            
            if not available_accounts:
                logger.warning("没有其他可用账号，使用原账号")
                available_accounts = list(all_cookies.keys())
            
            # 随机选择一个账号
            account_name = random.choice(available_accounts)
            cookie_json = all_cookies[account_name]
            
            logger.info(f"成功提取账号 {account_name} 的cookie信息")

            try:
                cookie_data = json.loads(cookie_json)
                if not isinstance(cookie_data, dict):
                    logger.error("从Redis获取的cookie信息不是有效的字典格式")
                    return {}
                
                # 保存当前使用的账号名
                cookie_data['current_account'] = account_name
                
                # 将选择的账号标记为正在使用 - 单线程使用Krickliu_single
                self.redis_is_using.set("Krickliu_single", account_name)
                self.current_cookie_account = account_name
                logger.success(f"已将账号 {account_name} 标记为正在使用")
                
                return cookie_data
            except json.JSONDecodeError as e:
                logger.error(f"从Redis获取的cookie信息不是有效的JSON格式: {e}")
                return {}
        except Exception as e:
            logger.error(f"从Redis获取cookie信息失败: {e}")
            return {}

    def setup_request_config(self):
        """设置请求配置"""
        if not self.cookie_data:
            logger.error("无法获取cookie数据")
            return {}, {}, {}
            
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9",
            "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
            "content-type": "application/json",
            "priority": "u=1, i",
            "referer": "",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36",
            "x-client-transaction-id": "+sz/dfqqmsPKXgIO3sJNwKfaqoxbAtx0ob9xnLCQVZAO9l1wXsSg5zt1Kv40mLB7gQA5//69ft5VtRYA2/w80LTF0Vul+Q",
            "x-csrf-token": self.cookie_data.get('csrf_token', ''),
            "x-twitter-active-user": "yes",
            "x-twitter-auth-type": "OAuth2Session",
            "x-twitter-client-language": "en",
            "x-xp-forwarded-for": "b040892a100113bad1e07d82e6c2bc14a7f57d62405d9c6df653b7f4a969d84323551abfe84f5252f82848a7e7e82130d5b0d9d05ccbac28aac810103b1f256eae1499112d35aaaeb227731e5f2495ef0ccd965ef7aea7bf71d959e523d19888a78f471c5eeb19957fe8d97e40f8403ea3459d382ae6d458f32d762ef0ff17ab50f75fff5b30e5ece98ea0e79913d5309a5a48fde144d67ae767f5bbc41af55429340a10285cac2bb6f246751f394cc9913884bbc7e829d64a860a23573927c415aa2b604ef3bf435bef879f156ef77f16ca05c4e6c29b4cde6bf01fb756c1a19c1f64064df8ffef1530a4467bb501ee56a5c3612bfef4f27034"
        }
        cookies = {
            "auth_token": self.cookie_data.get('auth_token', ''),
            "ct0": self.cookie_data.get('csrf_token', '')
        }
        # 正确设置代理IP
        proxy_ip = self.cookie_data.get('proxy_ip', '')
        proxy = {
            "http": proxy_ip,
            "https": proxy_ip
        } if proxy_ip else {}

        return headers, cookies, proxy

    def get_keywords_from_redis(self):
        """从Redis中获取关键词列表"""
        try:
            keywords = self.redis_client_keywords.all()
            if not keywords:
                logger.error("Redis中没有找到关键词")
                return []
            return [keyword for keyword in keywords.values()]
        except Exception as e:
            logger.error(f"从Redis获取关键词失败: {e}")
            return []

    def is_keyword_processed(self, keyword):
        """检查关键词是否已处理"""
        try:
            return bool(self.redis_processed_keywords.get(keyword))
        except Exception as e:
            logger.error(f"检查关键词处理状态失败: {e}")
            return False

    def mark_keyword_as_processed(self, keyword):
        """将关键词标记为已处理"""
        try:
            self.redis_processed_keywords.set(keyword, '1')
            logger.success(f"已将关键词 {keyword} 标记为处理完成")
        except Exception as e:
            logger.error(f"标记关键词处理状态失败: {e}")

    def clear_processed_keywords(self):
        """清除已处理的关键词记录"""
        try:
            # 删除整个redis_name对应的hash
            self.redis_processed_keywords.db.delete(self.redis_processed_keywords.redis_name)
            logger.success("已清除所有已处理的关键词记录")
        except Exception as e:
            logger.error(f"清除已处理关键词记录失败: {e}")

    def convert_twitter_time(self, twitter_time):
        """转换Twitter时间格式到标准格式"""
        try:
            # 增加空值检查
            if not twitter_time or twitter_time is None:
                logger.warning("Twitter时间为空，返回None")
                return None
            
            dt = datetime.strptime(twitter_time, '%a %b %d %H:%M:%S %z %Y')
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            logger.warning(f"时间转换失败: {e}, 输入值: {twitter_time}")
            return None

    def fetch_data(self, keyword, cursor=None):
        """发送请求获取数据"""
        url = "https://x.com/i/api/graphql/EP_W_cLzULmRd5E_X-PJkA/SearchTimeline"

        variables = {
            "rawQuery": keyword,
            "count": 20,
            "querySource": "typed_query",
            "product": "Top"
        }
        if cursor:
            variables["cursor"] = cursor

        # 确保中文关键词正确编码：使用ensure_ascii=False但不进行额外的URL编码
        variables_json = json.dumps(variables, ensure_ascii=False, separators=(',', ':'))

        params = self.base_params.copy()
        params["variables"] = variables_json

        # 对关键词进行URL编码用于referer
        encoded_keyword = urllib.parse.quote(keyword)

        # 使用实例的headers
        self.headers["referer"] = f"https://x.com/search?q={encoded_keyword}&src=typed_query&f=top"

        try:
            response = requests.get(
                url,
                headers=self.headers,
                cookies=self.cookies,
                params=params,
                proxies=self.proxy
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")
            return response.json()
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None

    def extract_entries(self, data, keyword, start_time=None):
        """从响应数据中提取目标字段
           start_time: 起始时间，格式为'YYYY-MM-DD HH:MM:SS'，早于此时间的推文将被过滤"""
        tweet_batch = []
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get(
                'timeline', {}).get('instructions', [])

            for instruction in instructions:
                if instruction.get('type') == 'TimelineAddEntries':
                    for entry in instruction.get('entries', []):
                        try:
                            result = entry.get('content', {}).get('itemContent', {}).get('tweet_results', {}).get(
                                'result', {})
                            if not result:
                                continue

                            user_result = result.get('core', {}).get('user_results', {}).get('result', {})
                            legacy_user = user_result.get('legacy', {})

                            quoted_tweet_id = result.get('quoted_status_result', {}).get('result', {}).get('legacy', {}).get('conversation_id_str', '')
                            retweeted_tweet_id = result.get('legacy', {}).get('retweeted_status_result', {}).get('result', {}).get('rest_id', 'null')

                            label_info = {}
                            if user_result.get('affiliates_highlighted_label', {}).get('label', {}):
                                label_data = user_result.get('affiliates_highlighted_label', {}).get('label', {})
                                label_info = {
                                    'description': label_data.get('description', ''),
                                    'userLabelDisplayType': label_data.get('userLabelDisplayType', ''),
                                    'userLabelType': label_data.get('userLabelType', '')
                                }
                            else:
                                label_info = {}

                            user_data = {
                                'user_id': user_result.get('rest_id'),
                                'username': legacy_user.get('screen_name'),
                                'created_at': self.convert_twitter_time(legacy_user.get('created_at')),
                                'location': legacy_user.get('location'),
                                'followers': legacy_user.get('followers_count', 0),
                                'following': legacy_user.get('friends_count', 0),
                                'posts': legacy_user.get('statuses_count', 0),
                                'verified': 1 if legacy_user.get('verified') else 0,
                                'verified_type': user_result.get('verified_type'),
                                'label': json.dumps(label_info, ensure_ascii=False) if label_info else None,
                                'description': legacy_user.get('description', '')
                            }

                            if user_data.get('user_id'):
                                self.save_user_to_mysql(user_data)
                            else:
                                logger.warning("用户数据缺少user_id，跳过保存")

                            tweet_result = result.get('legacy', {})
                            views_count = result.get('views', {})

                            edit_control = result.get('edit_control', {})
                            editable_until_msecs = edit_control.get('editable_until_msecs')
                            editable_time = None
                            if editable_until_msecs:
                                try:
                                    editable_time = datetime.fromtimestamp(
                                        int(editable_until_msecs) / 1000
                                    ).strftime('%Y-%m-%d %H:%M:%S')
                                except Exception as e:
                                    logger.warning(f"时间转换失败: {e}")

                            if start_time and editable_time:
                                if editable_time < start_time:
                                    logger.debug(f"推文时间 {editable_time} 早于起始时间 {start_time}，跳过保存")
                                    continue

                            username = legacy_user.get('screen_name')
                            tweet_id = tweet_result.get('id_str')
                            if username and tweet_id:
                                try:
                                    self.redis_comment_wait_spider.set(username, tweet_id)
                                    logger.success(f"成功保存用户推文映射 {username} -> {tweet_id} 到Redis")
                                except Exception as e:
                                    logger.error(f"保存用户推文映射到Redis失败: {e}")

                            tweet_data = {
                                'tweet_id': tweet_result.get('id_str'),
                                'user_id': user_data.get('user_id'),
                                'keyword': keyword,
                                'tweet_text': tweet_result.get('full_text', ''),
                                'tweet_time': editable_time,
                                'views': views_count.get('count', 0),
                                'replies': tweet_result.get('reply_count', 0),
                                'retweets': tweet_result.get('retweet_count', 0),
                                'favorites': tweet_result.get('favorite_count', 0),
                                'bookmarks': tweet_result.get('bookmark_count', 0),
                                'quoted_tweet_id': quoted_tweet_id,
                                'retweeted_tweet_id': retweeted_tweet_id
                            }

                            if tweet_data.get('tweet_id'):
                                self.save_tweet_to_mysql(tweet_data)
                                tweet_batch.append(tweet_data)
                            else:
                                logger.warning("推文数据缺少tweet_id，跳过保存")

                        except Exception as e:
                            logger.error(f"处理单条数据时出错: {e}")
                            continue

            return tweet_batch
        except Exception as e:
            logger.error(f"数据解析失败: {e}")
            return []

    def save_user_to_mysql(self, data):
        """将用户数据保存到MySQL"""
        try:
            if not data.get('user_id'):
                logger.warning(f"缺少user_id，无法保存用户数据")
                return

            username = data.get('username', '')
            if username and not username.startswith('@'):
                username = f"@{username}"

            sql = """
            INSERT INTO twitter_users (user_id, username, created_at, location, followers, 
                            following, posts, verified, verified_type, label, description)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            username=COALESCE(VALUES(username), username),
            followers=COALESCE(VALUES(followers), followers),
            following=COALESCE(VALUES(following), following),
            posts=COALESCE(VALUES(posts), posts),
            verified=COALESCE(VALUES(verified), verified),
            verified_type=COALESCE(VALUES(verified_type), verified_type),
            label=COALESCE(VALUES(label), label),
            description=COALESCE(VALUES(description), description)
            """
 
            values = [(
                data.get('user_id'),
                username,
                data.get('created_at'),
                data.get('location', ''),
                data.get('followers', 0),
                data.get('following', 0),
                data.get('posts', 0),
                data.get('verified', 0),
                data.get('verified_type', 'null'),
                data.get('label'),
                data.get('description', '')
            )]

            logger.debug(f"保存用户数据 - SQL参数数量: {len(values[0])}, 参数值: {values[0]}")
            super().insert_mysql(sql, values)
            logger.success(f"成功保存用户 ID:{data.get('user_id')} 用户名:{username} 到数据库")
        except Exception as e:
            logger.error(f"保存用户数据失败: {e}")

    def save_tweet_to_mysql(self, data):
        """将推文数据保存到MySQL"""
        try:
            if not data.get('tweet_id'):
                logger.warning(f"缺少tweet_id，无法保存推文数据")
                return

            sql = """
            INSERT INTO twitter_tweets (tweet_id, user_id, keyword, tweet_text, tweet_time,
                            views, replies, retweets, favorites, bookmarks, quoted_tweet_id, retweeted_tweet_id)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            user_id=COALESCE(VALUES(user_id), user_id),
            keyword=COALESCE(VALUES(keyword), keyword),
            tweet_text=COALESCE(VALUES(tweet_text), tweet_text),
            tweet_time=COALESCE(VALUES(tweet_time), tweet_time),
            views=COALESCE(VALUES(views), views),
            replies=COALESCE(VALUES(replies), replies),
            retweets=COALESCE(VALUES(retweets), retweets),
            favorites=COALESCE(VALUES(favorites), favorites),
            bookmarks=COALESCE(VALUES(bookmarks), bookmarks),
            quoted_tweet_id=COALESCE(VALUES(quoted_tweet_id), quoted_tweet_id),
            retweeted_tweet_id=COALESCE(VALUES(retweeted_tweet_id), retweeted_tweet_id)
            """

            values = [(
                data.get('tweet_id'),
                data.get('user_id', ''),
                data.get('keyword', ''),
                data.get('tweet_text', ''),
                data.get('tweet_time'),
                data.get('views', 0),
                data.get('replies', 0),
                data.get('retweets', 0),
                data.get('favorites', 0),
                data.get('bookmarks', 0),
                data.get('quoted_tweet_id', ''),
                data.get('retweeted_tweet_id', '')
            )]

            logger.debug(f"保存推文数据 - SQL参数数量: {len(values[0])}, 参数值: {values[0]}")
            super().insert_mysql(sql, values)
            logger.success(f"成功保存推文 ID:{data.get('tweet_id')} 到数据库")
        except Exception as e:
            logger.error(f"保存推文数据失败: {e}")

    def get_next_cursor(self, data):
        """翻页逻辑"""
        try:
            instructions = data.get('data', {}).get('search_by_raw_query', {}).get('search_timeline', {}).get(
                'timeline', {}).get('instructions', [])

            for instruction in instructions:
                entries = []
                if instruction.get('type') == 'TimelineAddEntries':
                    entries = instruction.get('entries', [])
                elif instruction.get('type') == 'TimelineReplaceEntry':
                    entry = instruction.get('entry')
                    if entry:
                        entries = [entry]

                logger.debug(f"处理 {instruction.get('type')} 指令，发现 {len(entries)} 个条目")

                for entry in entries:
                    entry_id = entry.get('entryId')
                    if entry_id == 'cursor-bottom-0' or (isinstance(entry_id, str) and 'cursor-bottom' in entry_id):
                        content = entry.get('content', {})
                        if content.get('entryType') == 'TimelineTimelineCursor':
                            cursor_value = content.get('value')
                            if cursor_value:
                                logger.success(f"成功提取游标: {cursor_value[:30]}...")
                                return cursor_value
            logger.warning("未找到有效游标")
            return None
        except Exception as e:
            logger.error(f"游标解析异常: {str(e)}")
            return None

    def refresh_cookie(self):
        """刷新cookie和请求配置"""
        logger.info("正在切换新的cookie...")
        
        # 获取当前使用的账号名
        current_account = self.cookie_data.get('current_account') if self.cookie_data else None
        
        # 先释放当前cookie
        if current_account:
            self.redis_is_using.delete("Krickliu_single")
            logger.info(f"已释放cookie: {current_account}")
        
        # 获取新的cookie，排除当前账号
        self.cookie_data = self.get_cookie_from_redis(exclude_account=current_account)
        self.headers, self.cookies, self.proxy = self.setup_request_config()
        
        if self.cookie_data:
            new_account = self.cookie_data.get('current_account', '未知')
            logger.success(f"成功切换到新的cookie: {new_account}")
            self.current_cookie_account = new_account
        else:
            logger.error("切换cookie失败")

    # 单线程处理方法已移除，仅使用多线程模式

    # 单线程运行方法已移除，仅使用多线程模式

    def get_available_cookie_for_thread(self, thread_id):
        """为指定线程获取可用的cookie，避开正在使用的cookie"""
        while True:
            try:
                # 获取正在使用的账号名列表
                using_cookies = self.redis_is_using.all()
                using_account_names = set(using_cookies.values()) if using_cookies else set()
                logger.info(f"线程{thread_id} - 当前正在使用的账号: {using_account_names}")
                
                # 获取所有可用的账号
                all_cookies = self.redis_client.all()
                if not all_cookies:
                    logger.error(f"线程{thread_id} - Redis中没有可用的cookie")
                    return {}, {}, {}, {}
                
                available_accounts = [account for account in all_cookies.keys() if account not in using_account_names]
                
                if not available_accounts:
                    logger.warning(f"线程{thread_id} - 所有cookie都在使用中，等待300秒后重试...")
                    time.sleep(300)
                    continue
                
                # 随机选择一个可用账号
                selected_account = random.choice(available_accounts)
                cookie_json = all_cookies[selected_account]
                
                logger.info(f"线程{thread_id} - 选择账号: {selected_account}")
                
                try:
                    cookie_data = json.loads(cookie_json)
                    if not isinstance(cookie_data, dict):
                        logger.error(f"线程{thread_id} - cookie信息不是有效的字典格式")
                        continue
                    
                    # 将选择的账号标记为正在使用
                    self.redis_is_using.set(f"Krickliu_thread_{thread_id}", selected_account)
                    logger.success(f"线程{thread_id} - 已将账号 {selected_account} 标记为正在使用")
                    
                    # 保存当前使用的账号名
                    cookie_data['current_account'] = selected_account
                    
                    headers = {
                        "authorization": "Bearer AAAAAAAAAAAAAAAAAAAAANRILgAAAAAAnNwIzUejRCOuH5E6I8xnZz4puTs%3D1Zv7ttfk8LF81IUq16cHjhLTvJu4FA33AGWWjCpTnA",
                        "x-client-transaction-id": "E2vhmIc2CD5UIs9c/eAlR5DxWVoB1UUP9RPW0WepLwpLp75JnCLVPAOyQ22B2K8hG0pgFxf7GLSd91JX2GUkAw8GHBYIEA",
                        "x-csrf-token": cookie_data.get('csrf_token', ''),
                        "x-twitter-active-user": "yes",
                        "x-twitter-auth-type": "OAuth2Session",
                        "x-twitter-client-language": "en",
                        "x-xp-forwarded-for": "b040892a100113bad1e07d82e6c2bc14a7f57d62405d9c6df653b7f4a969d84323551abfe84f5252f82848a7e7e82130d5b0d9d05ccbac28aac810103b1f256eae1499112d35aaaeb227731e5f2495ef0ccd965ef7aea7bf71d959e523d19888a78f471c5eeb19957fe8d97e40f8403ea3459d382ae6d458f32d762ef0ff17ab50f75fff5b30e5ece98ea0e79913d5309a5a48fde144d67ae767f5bbc41af55429340a10285cac2bb6f246751f394cc9913884bbc7e829d64a860a23573927c415aa2b604ef3bf435bef879f156ef77f16ca05c4e6c29b4cde6bf01fb756c1a19c1f64064df8ffef1530a4467bb501ee56a5c3612bfef4f27034"
                    }
                    cookies = {
                        "auth_token": cookie_data.get('auth_token', ''),
                        "ct0": cookie_data.get('csrf_token', '')
                    }
                    # 正确设置代理IP
                    proxy_ip = cookie_data.get('proxy_ip', '')
                    proxy = {
                        "http": proxy_ip,
                        "https": proxy_ip
                    } if proxy_ip else {}
                    
                    return headers, cookies, proxy, cookie_data
                    
                except json.JSONDecodeError as e:
                    logger.error(f"线程{thread_id} - cookie信息不是有效的JSON格式: {e}")
                    continue
                    
            except Exception as e:
                logger.error(f"线程{thread_id} - 获取可用cookie失败: {e}")
                time.sleep(10)
                continue

    def refresh_cookie_for_thread(self, thread_id, current_cookie_data):
        """为线程刷新cookie和请求配置"""
        logger.info(f"线程{thread_id} - 正在切换新的cookie...")
        
        # 获取当前使用的账号名
        current_account = current_cookie_data.get('current_account') if current_cookie_data else None
        
        # 先释放当前cookie
        if current_account:
            self.redis_is_using.delete(f"Krickliu_thread_{thread_id}")
            logger.info(f"线程{thread_id} - 已释放cookie: {current_account}")
        
        # 获取新的cookie，排除当前账号
        headers, cookies, proxy, new_cookie_data = self.get_available_cookie_for_thread(thread_id)
        
        if new_cookie_data:
            new_account = new_cookie_data.get('current_account', '未知')
            logger.success(f"线程{thread_id} - 成功切换到新的cookie: {new_account}")
            return headers, cookies, proxy, new_cookie_data
        else:
            logger.error(f"线程{thread_id} - 切换cookie失败")
            return None, None, None, None

    def process_keywords_thread(self, keywords, thread_id):
        """处理关键词列表 - 线程版本"""
        # 为当前线程获取可用的cookie和headers
        thread_headers, thread_cookies, thread_proxy, thread_cookie_data = self.get_available_cookie_for_thread(thread_id)
        if not thread_cookie_data:
            logger.error(f"线程{thread_id} - 无法获取cookie，线程退出")
            return
        
        logger.info(f"线程{thread_id} - 已获取可用cookie")

        try:
            for keyword in keywords:
                # 检查关键词是否已处理
                if self.is_keyword_processed(keyword):
                    logger.info(f"线程{thread_id} - 关键词 {keyword} 已处理，跳过")
                    continue

                logger.debug(f'线程{thread_id} - 开始处理关键词: {keyword}')
                next_cursor = None
                page = 1
                max_retry = 3  
                second_retry = 3  # 切换cookie后的重试次数
                any_page_success = False  # 标记是否有任何页面成功处理

                try:
                    while True:
                        # 页数限制检查
                        if self.max_pages and page > self.max_pages:
                            logger.info(f"线程{thread_id} - 已达到设置的最大页数 {self.max_pages}，停止翻页")
                            break

                        logger.info(f"线程{thread_id} - 正在爬取第 {page} 页...")
                        data = None
                        success = False

                        # 第一轮重试：使用当前cookie
                        for attempt in range(max_retry):
                            data = self.fetch_data_with_params(keyword, cursor=next_cursor, 
                                                             headers=thread_headers, cookies=thread_cookies, proxy=thread_proxy)

                            if data:
                                success = True
                                break
                            logger.warning(f"线程{thread_id} - 第 {attempt + 1} 次重试...")
                            time.sleep(random.uniform(180, 200))

                        # 如果第一轮重试失败，切换cookie后再试
                        if not success:
                            logger.warning(f"线程{thread_id} - 第一轮 {max_retry} 次重试全部失败，尝试切换cookie...")
                            new_headers, new_cookies, new_proxy, new_cookie_data = self.refresh_cookie_for_thread(thread_id, thread_cookie_data)
                            
                            if new_cookie_data:
                                thread_headers, thread_cookies, thread_proxy, thread_cookie_data = new_headers, new_cookies, new_proxy, new_cookie_data
                                
                                # 第二轮重试：使用新cookie
                                for attempt in range(second_retry):
                                    data = self.fetch_data_with_params(keyword, cursor=next_cursor,
                                                                     headers=thread_headers, cookies=thread_cookies, proxy=thread_proxy)

                                    if data:
                                        success = True
                                        break
                                    logger.warning(f"线程{thread_id} - 切换cookie后第 {attempt + 1} 次重试...")
                                    time.sleep(random.uniform(180, 200))

                        # 如果两轮重试都失败，跳过当前页面
                        if not success:
                            logger.error(f"线程{thread_id} - 关键词 {keyword} 在第 {page} 页处理失败，已尝试切换cookie")
                            # 如果已经成功获取了一些数据，则继续处理下一个关键词
                            if any_page_success:
                                logger.info(f"线程{thread_id} - 已成功处理了部分页面，继续处理下一个关键词")
                                break
                            else:
                                # 如果一个页面都没成功，则跳过这个关键词
                                logger.error(f"线程{thread_id} - 关键词 {keyword} 未能成功获取任何页面，跳过此关键词")
                                break

                        # 数据提取和保存
                        extracted = self.extract_entries(data, keyword, self.start_time)
                        if not extracted:
                            logger.warning(f"线程{thread_id} - 未提取到有效数据")
                            # 如果没有提取到数据，可能是到达了最后一页或者没有匹配的结果
                            if page == 1:
                                logger.info(f"线程{thread_id} - 关键词 {keyword} 第一页未提取到数据，可能没有匹配结果")
                                any_page_success = True  # 标记为成功，因为已经尝试过了
                            break
                        
                        # 标记有页面成功处理
                        any_page_success = True

                        # 获取下一页游标
                        new_cursor = self.get_next_cursor(data)
                        
                        # 检查游标是否有效
                        if not new_cursor:
                            logger.info(f"线程{thread_id} - 未找到下一页游标，到达最后一页")
                            break
                            
                        # 检查游标是否重复
                        if new_cursor == next_cursor:
                            logger.warning(f"线程{thread_id} - 获取到重复游标，停止翻页")
                            break

                        # 更新循环终止条件
                        if self.max_pages and page >= self.max_pages:
                            logger.info(f"线程{thread_id} - 已达到设置的最大页数 {self.max_pages}")
                            break

                        # 更新游标并继续
                        next_cursor = new_cursor
                        page += 1

                        # 随机等待
                        wait_time = random.uniform(180, 200)
                        logger.info(f"线程{thread_id} - 等待 {wait_time:.1f} 秒后继续")
                        time.sleep(wait_time)

                    # 只有成功处理完至少一页的情况下才标记为已处理
                    if any_page_success:
                        self.mark_keyword_as_processed(keyword)
                        logger.success(f"线程{thread_id} - 完成关键词 {keyword} 的采集")
                    else:
                        logger.warning(f"线程{thread_id} - 关键词 {keyword} 未能成功处理，不标记为已完成")

                except Exception as e:
                    logger.error(f"线程{thread_id} - 处理关键词 {keyword} 时发生错误: {e}")
                    continue

        except Exception as e:
            logger.error(f"线程{thread_id} - 线程执行失败: {e}")
        finally:
            # 线程结束时清理cookie记录
            try:
                self.redis_is_using.delete(f"Krickliu_thread_{thread_id}")
                logger.info(f"线程{thread_id} - 已清理cookie使用记录")
            except Exception as e:
                logger.error(f"线程{thread_id} - 清理cookie记录失败: {e}")

    def fetch_data_with_params(self, keyword, cursor=None, headers=None, cookies=None, proxy=None):
        """发送请求获取数据 - 支持自定义参数"""
        url = "https://x.com/i/api/graphql/EP_W_cLzULmRd5E_X-PJkA/SearchTimeline"

        variables = {
            "rawQuery": keyword,
            "count": 20,
            "querySource": "typed_query",
            "product": "Top"
        }
        if cursor:
            variables["cursor"] = cursor

        # 确保中文关键词正确编码：使用ensure_ascii=False但不进行额外的URL编码
        variables_json = json.dumps(variables, ensure_ascii=False, separators=(',', ':'))

        params = self.base_params.copy()
        params["variables"] = variables_json

        # 对关键词进行URL编码用于referer
        encoded_keyword = urllib.parse.quote(keyword)

        # 使用传入的headers或默认headers
        request_headers = headers or self.headers
        request_headers["referer"] = f"https://x.com/search?q={encoded_keyword}&src=typed_query&f=top"

        try:
            # 修复：确保即使传入空字典也不会回退到全局配置
            request_cookies = cookies if cookies is not None else self.cookies
            request_proxies = proxy if proxy is not None else self.proxy
            
            # 添加详细日志以便调试
            if headers:
                csrf_token = headers.get('x-csrf-token', '未知')
                logger.debug(f"使用线程专属headers，x-csrf-token: {csrf_token[:10]}...")
            else:
                logger.warning("使用全局headers")
                
            if cookies:
                auth_token = cookies.get('auth_token', '未知')
                logger.debug(f"使用线程专属cookies，auth_token: {auth_token[:5]}...")
            else:
                logger.warning("使用全局cookies")
                
            if proxy:
                proxy_info = str(proxy)[:50]
                logger.debug(f"使用线程专属代理: {proxy_info}...")
            else:
                logger.warning("使用全局代理")
            
            # 添加超时设置，避免请求卡住
            response = requests.get(
                url,
                headers=request_headers,
                cookies=request_cookies,
                params=params,
                proxies=request_proxies,
                timeout=30  # 设置30秒超时
            )
            response.raise_for_status()
            logger.success(f"请求成功 [{response.status_code}]")
            return response.json()
        except requests.exceptions.Timeout:
            logger.error(f"请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"请求异常: {e}")
            return None
        except Exception as e:
            logger.error(f"请求失败: {e}")
            return None

    def run_with_threads(self, num_threads=1):
        """多线程运行方法"""
        keywords = self.get_keywords_from_redis()
        if not keywords:
            logger.error("未从Redis中获取到关键词，程序终止")
            return
            
        # 清理之前可能残留的线程cookie记录
        self.cleanup_all_thread_cookies()
        
        # 根据关键词数量动态调整线程数
        actual_num_threads = min(num_threads, len(keywords))
        if actual_num_threads < num_threads:
            logger.warning(f"关键词数量({len(keywords)})小于设定的线程数({num_threads})，调整为使用{actual_num_threads}个线程")
        
        # 将关键词平均分配给不同线程
        keywords_per_thread = len(keywords) // actual_num_threads
        keyword_chunks = []

        for i in range(actual_num_threads):
            start_idx = i * keywords_per_thread
            if i == actual_num_threads - 1:  # 最后一个线程处理剩余的关键词
                end_idx = len(keywords)
            else:
                end_idx = (i + 1) * keywords_per_thread
            keyword_chunks.append(keywords[start_idx:end_idx])

        logger.info(f"开始运行 {actual_num_threads} 个线程，关键词分配: {[len(chunk) for chunk in keyword_chunks]}")

        # 创建并启动线程，每个线程延迟10秒启动
        threads = []
        for i, keyword_chunk in enumerate(keyword_chunks):
            if keyword_chunk:  # 只为非空的关键词列表创建线程
                thread = threading.Thread(
                    target=self.process_keywords_thread,
                    args=(keyword_chunk, i + 1),
                    name=f"KeywordThread-{i + 1}"
                )
                threads.append(thread)
                thread.start()
                logger.info(f"启动线程 {i + 1}，处理 {len(keyword_chunk)} 个关键词")
                
                # 延迟10秒再启动下一个线程，确保每个线程都能获取到独立的cookie
                if i < len(keyword_chunks) - 1:  # 最后一个线程不需要延迟
                    logger.info("等待10秒后启动下一个线程...")
                    time.sleep(10)

        # 等待所有线程完成
        for i, thread in enumerate(threads):
            thread.join()
            logger.info(f"线程 {i + 1} 已完成")

        logger.success("所有线程已完成关键词处理")


if __name__ == "__main__":
    start_time = "2025-07-29 00:00:00"
    spider = XSpider(max_pages=1, start_time=start_time)
    
    try:
        logger.info("启动多线程爬虫，使用10个线程")
        spider.run_with_threads(num_threads=1)  # 多线程运行
            
    except KeyboardInterrupt:
        logger.info("用户中断程序")
        spider.cleanup_on_exit()
    except Exception as e:
        logger.error(f"程序运行出错: {e}")
        spider.cleanup_on_exit()
    
    # spider.clear_processed_keywords()  # 如需要可以清除已处理关键词记录